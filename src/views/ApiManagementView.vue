<script setup lang="ts">
	import { useCurNav } from "@/stores/curNav";
	import { defineAsyncComponent } from "vue";
	import component_wait from "@/components/utils/component_wait.vue";
	import { useI18n } from "vue-i18n";

	const ApiManagement = defineAsyncComponent(
		() => import("@/components/api-management/api-management.vue")
	);

	const curNavStore = useCurNav();
	curNavStore.setCurNav("api_management");

	const { t } = useI18n();
</script>

<template>
	<div class="min-h-screen bg-gray-100 flex flex-col">
		<div class="flex-grow p-8">
			<div class="mx-auto">
				<div class="bg-base-100 shadow-md rounded-lg overflow-hidden">
					<div class="p-6 border-b border-gray-200">
						<h1 class="text-2xl font-bold text-gray-900">
							{{ t("api_management") }}
						</h1>
					</div>

					<div class="p-4 sm:p-6">
						<component_wait>
							<ApiManagement />
						</component_wait>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
