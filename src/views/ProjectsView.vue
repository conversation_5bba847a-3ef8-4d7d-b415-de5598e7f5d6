<script setup lang="ts">
	import { type Project, getProjects, deleteProject, cloneProject } from "@/api/projects";
	import { useCurNav } from "@/stores/curNav";
	import { ref, onMounted, onBeforeUnmount, watch, defineAsyncComponent } from "vue";
	import { useRouter } from "vue-router";
	import {
		RocketLaunchIcon,
		PencilSquareIcon,
		TrashIcon,
		DocumentDuplicateIcon,
		DocumentPlusIcon,
	} from "@heroicons/vue/24/outline";
	import { EllipsisVerticalIcon } from "@heroicons/vue/20/solid";
	import { useI18n } from "vue-i18n";
	import { Error } from "@/utils/notify";
	const { t } = useI18n();
	const Page = defineAsyncComponent(() => import("@/components/utils/page.vue"));
	const curNavStore = useCurNav();
	curNavStore.setCurNav("projects");

	type myProject = {
		showDropdown: boolean;
		dropdownPosition: { top: number; left: number };
	} & Project;

	const projects = ref<myProject[]>([]);
	const total = ref(0);
	const page = ref(1);
	const pageSize = ref(16);
	const loading = ref(false);
	const load = () => {
		loading.value = true;
		getProjects(page.value, pageSize.value)
			.then((res) => {
				projects.value = res.list.map((project: Project) => ({
					...project,
					showDropdown: false,
					dropdownPosition: { top: 0, left: 0 },
				}));
				total.value = res.total;
			})
			.finally(() => {
				loading.value = false;
			});
	};
	watch([page, pageSize], load);
	load();

	const toggleDropdown = (uuid: string, event: MouseEvent) => {
		event.stopPropagation();
		const project = projects.value.find((p) => p.uuid === uuid);
		if (project) {
			project.showDropdown = !project.showDropdown;
			project.dropdownPosition = { top: event.clientY, left: event.clientX };
		}
	};

	const handleClickOutside = (event: MouseEvent) => {
		projects.value.forEach((project) => {
			if (project.showDropdown) {
				project.showDropdown = false;
			}
		});
	};

	onMounted(() => {
		document.addEventListener("click", handleClickOutside);
	});

	onBeforeUnmount(() => {
		document.removeEventListener("click", handleClickOutside);
	});

	let options = [
		{
			label: t("create-copy"),
			icon: DocumentDuplicateIcon,
			action: (project: myProject) => {
				cloneProject(project.uuid)
					.then((res) => {
						load();
					})
					.catch((error) => {
						console.error("克隆项目失败:", error);
						Error(t("create-copy-failed"), error || t("operation-failed"));
					});
			},
		},
		{
			label: t("delete"),
			icon: TrashIcon,
			action: (project: myProject) => {
				let id = project.uuid;
				if (confirm(t("confirm-delete-project"))) {
					deleteProject(id)
						.then(() => {
							load();
						})
						.catch((error) => {
							console.error("删除项目失败:", error);
							Error(t("delete-failed"), error || t("operation-failed"));
						});
				}
			},
		},
	];

	const router = useRouter();

	const createNewWorkflow = (example: string = "") => {
		router.push({
			name: "workflow",
			params: { id: "new" },
			query: { example },
		});
	};
</script>

<template>
	<div class="bg-gray-100 min-h-full p-4">
		<!-- <h1 class="text-3xl font-bold mb-6 text-gray-800">我的项目</h1> -->
		<div
			v-if="loading"
			class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
		>
			<div
				v-for="i in 12"
				:key="i"
				class="bg-white rounded-lg shadow-md p-4 animate-pulse"
			>
				<div class="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
				<div class="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
				<div class="h-4 bg-gray-200 rounded w-1/4"></div>
			</div>
		</div>
		<div v-else-if="projects.length === 0" class="text-center py-13">
			<div class="max-w-2xl mx-auto text-center space-y-6">
				<img src="@/assets/empty-projects.svg" class="mx-auto w-48" />
				<div class="space-y-6">
					<div>
						<h2 class="text-2xl font-semibold text-gray-700">
							{{ t("no-projects") }}
						</h2>
						<p class="mt-2 text-gray-500">
							{{ t("click-button-to-start-automation") }}
						</p>
					</div>
					<div>
						<button
							@click="createNewWorkflow()"
							class="btn btn-primary inline-flex items-center px-8 py-3"
						>
							<DocumentPlusIcon class="w-5 h-5 mr-2" />
							{{ t("create-blank-workflow") }}
						</button>
					</div>
					<div class="border-t border-gray-200 pt-6">
						<p class="text-sm text-gray-500 mb-6">
							{{ t("or-start-from-example") }}
						</p>
						<div class="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-4xl mx-auto">
							<div
								class="bg-white rounded-lg shadow py-4 px-3 hover:shadow-md transition-shadow relative"
							>
								<div
									class="absolute -top-3 left-4 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
								>
									{{ t("example") }}
								</div>
								<div class="flex items-center mb-2">
									<DocumentDuplicateIcon
										class="w-5 h-5 text-blue-600 mr-2"
									/>
									<h3 class="text-base font-medium text-left break-all">
										{{ t("llm-data-processing") }}
									</h3>
								</div>
								<p class="text-sm text-gray-600 mb-3 text-left">
									{{ t("llm-data-processing-description") }}
								</p>
								<button
									@click="createNewWorkflow('llmDataProcessing')"
									class="btn btn-primary btn-sm w-full"
								>
									{{ t("use-this-example") }}
								</button>
							</div>

							<div
								class="bg-white rounded-lg shadow py-4 px-3 hover:shadow-md transition-shadow relative"
							>
								<div
									class="absolute -top-3 left-4 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full"
								>
									{{ t("example") }}
								</div>
								<div class="flex items-center mb-2">
									<RocketLaunchIcon class="w-5 h-5 text-green-600 mr-2" />
									<h3 class="text-base font-medium text-left break-all">
										{{ t("multi-llm-assistant") }}
									</h3>
								</div>
								<p class="text-sm text-gray-600 mb-3 text-left">
									{{ t("integrate-multiple-llm-models") }}
								</p>
								<button
									@click="createNewWorkflow('multiLlmAssistant')"
									class="btn btn-primary btn-sm w-full"
								>
									{{ t("use-this-example") }}
								</button>
							</div>

							<div
								class="bg-white rounded-lg shadow py-4 px-3 hover:shadow-md transition-shadow relative"
							>
								<div
									class="absolute -top-3 left-4 px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full"
								>
									{{ t("example") }}
								</div>
								<div class="flex items-center mb-2">
									<DocumentPlusIcon class="w-5 h-5 text-purple-600 mr-2" />
									<h3 class="text-base font-medium text-left break-all">
										{{ t("logic-flow") }}
									</h3>
								</div>
								<p class="text-sm text-gray-600 mb-3 text-left">
									{{ t("visual-logic-flow") }}
								</p>
								<button
									@click="createNewWorkflow('logicFlow')"
									class="btn btn-primary btn-sm w-full"
								>
									{{ t("use-this-example") }}
								</button>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div
			v-else
			class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
		>
			<div
				v-for="i in projects"
				:key="i.uuid"
				class="bg-white rounded-lg shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg"
				:data-project-id="i.uuid"
			>
				<div class="p-4">
					<h2 class="text-xl font-semibold mb-2 text-gray-800">
						{{ i.name || t("unnamed-project") }}
					</h2>
					<p class="text-sm text-gray-600 mb-4 min-h-[3em]">
						{{ i.description || t("no-description") }}
					</p>
					<p class="text-xs text-gray-400 mb-4">
						{{
							new Date(i.created_at).toLocaleDateString() +
							" " +
							new Date(i.created_at).toLocaleTimeString()
						}}
					</p>
					<div class="flex justify-between items-center">
						<div class="relative">
							<button
								class="text-gray-500 focus:outline-none bg-gray-50 p-1 rounded-md hover:bg-gray-200 transition-all duration-300"
								@click="toggleDropdown(i.uuid, $event)"
							>
								<EllipsisVerticalIcon class="w-5 h-5" />
							</button>
							<Teleport to="body" v-if="i.showDropdown">
								<div
									v-motion-pop
									class="absolute z-10 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5"
									:style="{
										top: i.dropdownPosition.top + 'px',
										left: i.dropdownPosition.left + 'px',
									}"
								>
									<div class="py-1">
										<a
											v-for="option in options"
											:key="option.label"
											@click="option.action(i)"
											class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer"
										>
											<component
												:is="option.icon"
												class="w-4 h-4 inline-block mr-2"
											/>
											{{ option.label }}
										</a>
									</div>
								</div>
							</Teleport>
						</div>
						<div class="space-x-2">
							<RouterLink
								:to="{ name: 'runtime', params: { id: i.uuid } }"
								class="btn btn-primary btn-outline inline-flex items-center btn-sm"
							>
								<RocketLaunchIcon class="w-4 h-4" />
								{{ t("run") }}
							</RouterLink>
							<RouterLink
								:to="{ name: 'workflow', params: { id: i.uuid } }"
								class="btn btn-primary inline-flex items-center btn-sm"
							>
								<PencilSquareIcon class="w-4 h-4" />
								{{ t("edit") }}
							</RouterLink>
						</div>
					</div>
				</div>
			</div>
		</div>
		<Page
			v-if="projects.length > 0"
			:page="page"
			:pageSize="pageSize"
			:total="total"
			@change="(e) => (page = e)"
			class="mt-8"
		></Page>
	</div>
</template>
