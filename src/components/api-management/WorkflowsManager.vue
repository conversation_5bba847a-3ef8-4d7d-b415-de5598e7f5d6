<script setup lang="ts">
	import { ref, onMounted, defineAsyncComponent } from "vue";
	import {
		listPublishedWorkflows,
		updatePublishedWorkflowStatus,
		deletePublishedWorkflow,
		type PublishedWorkflowItem,
		getPublishedWorkflow,
	} from "@/api/publish";
	import { useI18n } from "vue-i18n";
	import { ClipboardIcon } from "@heroicons/vue/24/outline";
	import { Error, Success } from "@/utils/notify";
	import { parseWorkflowInput, generateExampleInputs } from "@/utils/input_processor";
	import { listApiKeys } from "@/api/api_key";
	import ConfirmModal from "@/components/api-management/modals/ConfirmModal.vue";
	import ApiExampleModal from "@/components/api-management/modals/ApiExampleModal.vue";

	const { t } = useI18n();

	// 确认对话框状态
	const showConfirmModal = ref(false);
	const confirmModalTitle = ref("");
	const confirmModalMessage = ref("");
	const confirmModalCallback = ref<(() => void) | null>(null);

	// 请求示例对话框状态
	const showExampleModal = ref(false);
	const isLoadingExample = ref(false);
	const currentWorkflowExample = ref<{
		uuid: string;
		apiKey: string;
		inputParams: Record<string, any>;
	} | null>(null);
	const activeExampleTab = ref("curl");

	// Published Workflows 状态
	const publishedWorkflows = ref<PublishedWorkflowItem[]>([]);
	const isLoadingWorkflows = ref(false);
	const isUpdatingStatus = ref(false);
	const updatingWorkflowId = ref<string | null>(null);

	// 复制到剪贴板
	const copyToClipboard = (text: string) => {
		if (navigator && navigator.clipboard) {
			navigator.clipboard
				.writeText(text)
				.then(() => {
					Success(t("copy_success"), t("copied_to_clipboard"));
				})
				.catch((err) => {
					console.error("复制失败:", err);
					Error(t("error"), t("copy_to_clipboard_failed"));
				});
		}
	};

	// 获取已发布的工作流
	const fetchPublishedWorkflows = async () => {
		if (publishedWorkflows.value.length === 0) {
			isLoadingWorkflows.value = true;
		}
		try {
			const res = await listPublishedWorkflows();
			publishedWorkflows.value = res.list;
		} catch (error) {
			console.error("获取已发布工作流失败:", error);
		} finally {
			isLoadingWorkflows.value = false;
		}
	};

	// 更新工作流状态
	const updateWorkflowStatus = async (workflow: any, isActive: boolean) => {
		updatingWorkflowId.value = workflow.uuid;
		isUpdatingStatus.value = true;

		// 先乐观更新UI
		const workflowIndex = publishedWorkflows.value.findIndex(
			(w) => w.uuid === workflow.uuid
		);
		if (workflowIndex !== -1) {
			publishedWorkflows.value[workflowIndex].is_active = isActive;
		}

		try {
			await updatePublishedWorkflowStatus(workflow.uuid, isActive);
			// 成功后静默刷新数据
			const res = await listPublishedWorkflows();
			publishedWorkflows.value = res.list;
		} catch (error) {
			console.error("更新工作流状态失败:", error);
			// 如果失败，恢复原状态
			if (workflowIndex !== -1) {
				publishedWorkflows.value[workflowIndex].is_active = !isActive;
			}
		} finally {
			isUpdatingStatus.value = false;
			updatingWorkflowId.value = null;
		}
	};

	// 显示确认对话框
	const showConfirm = (title: string, message: string, callback: () => void) => {
		confirmModalTitle.value = title;
		confirmModalMessage.value = message;
		confirmModalCallback.value = callback;
		showConfirmModal.value = true;
	};

	// 处理确认
	const handleConfirm = () => {
		if (confirmModalCallback.value) {
			confirmModalCallback.value();
		}
		showConfirmModal.value = false;
	};

	// 取消确认
	const handleCancel = () => {
		showConfirmModal.value = false;
	};

	// 删除已发布的工作流
	const handleDeleteWorkflow = async (id: string) => {
		showConfirm(t("confirm_delete"), t("confirm_delete_workflow"), async () => {
			try {
				await deletePublishedWorkflow(id);
				await fetchPublishedWorkflows();
			} catch (error) {
				console.error("删除已发布工作流失败:", error);
			}
		});
	};

	// 显示API请求示例
	const showApiExample = async (workflow: PublishedWorkflowItem) => {
		// 显示模态框
		showExampleModal.value = true;
		isLoadingExample.value = true;

		// 获取API Keys
		try {
			const apiKeys = await listApiKeys();
			// 如果有API Keys，默认使用第一个
			const defaultApiKey = apiKeys.length > 0 ? apiKeys[0].key : "YOUR_API_KEY";

			// 获取工作流的详细信息以生成输入参数示例
			try {
				const workflowData = await getPublishedWorkflow(workflow.uuid);
				let inputParams: Record<string, any> = {};

				// 如果工作流数据中有输入节点信息
				if (workflowData && workflowData.data) {
					const data = JSON.parse(workflowData.data);
					// 使用公共输入处理模块解析工作流输入
					const { inputMeta } = parseWorkflowInput(data);
					// 使用公共模块生成示例输入值
					inputParams = generateExampleInputs(inputMeta);
				}

				currentWorkflowExample.value = {
					uuid: workflow.uuid,
					apiKey: defaultApiKey,
					inputParams: inputParams,
				};
			} catch (error) {
				console.error("获取工作流详细信息失败:", error);
				// 如果获取失败，仍然显示示例但使用空输入
				currentWorkflowExample.value = {
					uuid: workflow.uuid,
					apiKey: defaultApiKey,
					inputParams: {},
				};
			}
		} catch (error) {
			console.error("获取API Keys失败:", error);
			// 如果获取失败，使用默认值
			currentWorkflowExample.value = {
				uuid: workflow.uuid,
				apiKey: "YOUR_API_KEY",
				inputParams: {},
			};
		} finally {
			isLoadingExample.value = false;
		}
	};

	// 页面加载时获取数据
	onMounted(() => {
		fetchPublishedWorkflows();
	});
</script>

<template>
	<div>
		<h3 class="text-lg font-semibold mb-4">{{ t("published_workflows") }}</h3>

		<!-- Published Workflows 列表 -->
		<div class="overflow-x-auto">
			<table class="table w-full">
				<thead>
					<tr>
						<th>UUID</th>
						<th>{{ t("name") }}</th>
						<th>{{ t("version") }}</th>
						<th>{{ t("description") }}</th>
						<th>{{ t("status") }}</th>
						<th>{{ t("actions") }}</th>
					</tr>
				</thead>
				<tbody>
					<tr v-if="isLoadingWorkflows">
						<td colspan="6" class="text-center py-4">{{ t("loading") }}...</td>
					</tr>
					<tr v-else-if="publishedWorkflows.length === 0">
						<td colspan="6" class="text-center py-4">
							{{ t("no_published_workflows") }}
						</td>
					</tr>
					<tr v-for="workflow in publishedWorkflows" :key="workflow.uuid">
						<td>
							<div class="flex items-center">
								<span class="truncate max-w-xs"
									>{{ workflow.uuid.slice(0, 10) }}...</span
								>
								<button
									@click="copyToClipboard(workflow.uuid)"
									class="btn btn-ghost btn-xs"
								>
									<ClipboardIcon class="w-4 h-4" />
								</button>
							</div>
						</td>
						<td>{{ workflow.name }}</td>
						<td>{{ workflow.version }}</td>
						<td class="truncate max-w-xs">
							{{ workflow.description || t("no_description") }}
						</td>
						<td>
							<div class="flex items-center space-x-2">
								<input
									type="checkbox"
									class="toggle toggle-sm toggle-success"
									:checked="workflow.is_active"
									@change="
										updateWorkflowStatus(workflow, !workflow.is_active)
									"
									:disabled="
										isUpdatingStatus &&
										updatingWorkflowId === workflow.uuid
									"
								/>
								<span
									class="text-xs font-medium"
									:class="
										workflow.is_active ? 'text-success' : 'text-warning'
									"
								>
									{{ workflow.is_active ? t("active") : t("inactive") }}
								</span>
								<span
									v-if="
										isUpdatingStatus &&
										updatingWorkflowId === workflow.uuid
									"
									class="loading loading-spinner loading-xs"
								></span>
							</div>
						</td>
						<td>
							<div class="flex space-x-2">
								<button @click="showApiExample(workflow)" class="btn btn-xs">
									{{ t("example") }}
								</button>
								<button
									@click="handleDeleteWorkflow(workflow.uuid)"
									class="btn btn-error btn-xs"
								>
									{{ t("delete") }}
								</button>
							</div>
						</td>
					</tr>
				</tbody>
			</table>
		</div>

		<Teleport to="body">
			<!-- 确认对话框 -->
			<ConfirmModal
				v-if="showConfirmModal"
				:title="confirmModalTitle"
				:message="confirmModalMessage"
				@confirm="handleConfirm"
				@cancel="handleCancel"
			/>

			<!-- API请求示例对话框 -->
			<ApiExampleModal
				v-if="showExampleModal"
				:is-loading="isLoadingExample"
				:example="currentWorkflowExample"
				:active-tab="activeExampleTab"
				@close="showExampleModal = false"
				@change-tab="activeExampleTab = $event"
				@copy-uuid="copyToClipboard($event)"
			/>
		</Teleport>
	</div>
</template>
