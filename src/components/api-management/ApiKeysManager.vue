<script setup lang="ts">
	import { ref, onMounted } from "vue";
	import { type APIKey, listApi<PERSON><PERSON><PERSON>, create<PERSON>pi<PERSON><PERSON>, deleteApi<PERSON>ey } from "@/api/api_key";
	import { useI18n } from "vue-i18n";
	import { ClipboardIcon } from "@heroicons/vue/24/outline";
	import { Error, Success } from "@/utils/notify";

	const { t } = useI18n();

	// API Keys 状态
	const apiKeys = ref<APIKey[]>([]);
	const isLoadingKeys = ref(false);
	const newKeyName = ref("");
	const newKeyExpireDays = ref(30);
	const isCreatingKey = ref(false);
	const showNewKeyModal = ref(false);
	const newKeyData = ref<{ key: string; name: string } | null>(null);

	// 确认对话框状态
	const showConfirmModal = ref(false);
	const confirmModalTitle = ref("");
	const confirmModalMessage = ref("");
	const confirmModalCallback = ref<(() => void) | null>(null);

	// 复制到剪贴板
	const copyToClipboard = (text: string) => {
		if (navigator && navigator.clipboard) {
			navigator.clipboard
				.writeText(text)
				.then(() => {
					Success(t("copy_success"), t("copied_to_clipboard"));
				})
				.catch((err) => {
					console.error(err);
					Error(t("error"), t("copy_to_clipboard_failed"));
				});
		}
	};

	// 获取 API Keys
	const fetchApiKeys = async () => {
		isLoadingKeys.value = true;
		try {
			apiKeys.value = (await listApiKeys()) || [];
		} catch (error) {
			console.error(error);
		} finally {
			isLoadingKeys.value = false;
		}
	};

	// 创建新的 API Key
	const handleCreateKey = async () => {
		if (!newKeyName.value) return;

		isCreatingKey.value = true;
		try {
			const data = await createApiKey({
				name: newKeyName.value,
				expires_in_days: newKeyExpireDays.value,
			});
			newKeyData.value = data;
			await fetchApiKeys();
		} catch (error) {
			console.error(error);
		} finally {
			isCreatingKey.value = false;
		}
	};

	// 显示确认对话框
	const showConfirm = (title: string, message: string, callback: () => void) => {
		confirmModalTitle.value = title;
		confirmModalMessage.value = message;
		confirmModalCallback.value = callback;
		showConfirmModal.value = true;
	};

	// 处理确认
	const handleConfirm = () => {
		if (confirmModalCallback.value) {
			confirmModalCallback.value();
		}
		showConfirmModal.value = false;
	};

	// 取消确认
	const handleCancel = () => {
		showConfirmModal.value = false;
	};

	// 删除 API Key
	const handleDeleteKey = async (id: number) => {
		showConfirm(t("confirm_delete"), t("confirm_delete_api_key"), async () => {
			try {
				await deleteApiKey(id);
				await fetchApiKeys();
			} catch (error) {
				console.error(error);
			}
		});
	};

	// 格式化日期
	const formatDate = (dateString: string) => {
		if (!dateString) return "-";
		const date = new Date(dateString);
		return isNaN(date.getTime()) ? "-" : date.toLocaleString();
	};

	// 页面加载时获取数据
	onMounted(() => {
		fetchApiKeys();
	});
</script>

<template>
	<div>
		<div class="flex justify-between items-center mb-4">
			<h3 class="text-lg font-semibold">{{ t("api_keys") }}</h3>
			<button @click="showNewKeyModal = true" class="btn btn-primary btn-sm">
				{{ t("create_new_key") }}
			</button>
		</div>

		<!-- API Keys 列表 -->
		<div class="overflow-x-auto">
			<table class="table w-full">
				<thead>
					<tr>
						<th>{{ t("name") }}</th>
						<th>{{ t("key") }}</th>
						<th>{{ t("created_at") }}</th>
						<th>{{ t("expires_at") }}</th>
						<th>{{ t("last_used") }}</th>
						<th>{{ t("actions") }}</th>
					</tr>
				</thead>
				<tbody>
					<tr v-if="isLoadingKeys">
						<td colspan="6" class="text-center py-4">{{ t("loading") }}...</td>
					</tr>
					<tr v-else-if="apiKeys.length === 0">
						<td colspan="6" class="text-center py-4">{{ t("no_api_keys") }}</td>
					</tr>
					<tr v-for="key in apiKeys" :key="key.id">
						<td>{{ key.name }}</td>
						<td>
							<div class="flex items-center">
								<span class="truncate max-w-xs"
									>{{ key.key.substring(0, 8) }}...</span
								>
								<button
									@click="copyToClipboard(key.key)"
									class="btn btn-ghost btn-xs"
								>
									<ClipboardIcon class="w-4 h-4" />
								</button>
							</div>
						</td>
						<td>{{ formatDate(key.created_at) }}</td>
						<td>{{ formatDate(key.expires_at) }}</td>
						<td>{{ key.last_used ? formatDate(key.last_used) : t("never") }}</td>
						<td>
							<button
								@click="handleDeleteKey(key.id)"
								class="btn btn-error btn-xs"
							>
								{{ t("delete") }}
							</button>
						</td>
					</tr>
				</tbody>
			</table>
		</div>
		<Teleport to="body">
			<!-- 创建新API Key的模态框 -->
			<div v-if="showNewKeyModal" class="modal modal-open">
				<div class="modal-box">
					<h3 class="font-bold text-lg">{{ t("create_new_api_key") }}</h3>
					<div class="py-4">
						<div class="form-control w-full">
							<label class="label">
								<span class="label-text">{{ t("key_name") }}</span>
							</label>
							<input
								v-model="newKeyName"
								type="text"
								:placeholder="t('enter_key_name')"
								class="input input-bordered w-full"
							/>
						</div>
						<div class="form-control w-full mt-4">
							<label class="label">
								<span class="label-text">{{ t("expires_in_days") }}</span>
							</label>
							<input
								v-model="newKeyExpireDays"
								type="number"
								min="1"
								max="365"
								class="input input-bordered w-full"
							/>
						</div>
					</div>
					<div class="modal-action">
						<button @click="showNewKeyModal = false" class="btn">
							{{ t("cancel") }}
						</button>
						<button
							@click="
								handleCreateKey();
								showNewKeyModal = false;
							"
							class="btn btn-primary"
							:disabled="!newKeyName || isCreatingKey"
						>
							{{ isCreatingKey ? `${t("creating")}...` : t("create") }}
						</button>
					</div>
				</div>
				<div class="modal-backdrop" @click="showNewKeyModal = false"></div>
			</div>

			<!-- 显示新创建的API Key的模态框 -->
			<div v-if="newKeyData" class="modal modal-open">
				<div class="modal-box">
					<h3 class="font-bold text-lg">{{ t("api_key_created") }}</h3>
					<div class="py-4">
						<p class="text-sm text-warning mb-2">{{ t("key_warning") }}</p>
						<div class="form-control">
							<label class="label">
								<span class="label-text">{{ t("key_name") }}</span>
							</label>
							<input
								type="text"
								:value="newKeyData.name"
								class="input input-bordered"
								readonly
							/>
						</div>
						<div class="form-control mt-4">
							<label class="label">
								<span class="label-text">{{ t("api_key") }}</span>
							</label>
							<div class="flex">
								<input
									type="text"
									:value="newKeyData.key"
									class="input input-bordered flex-grow"
									readonly
								/>
								<button
									@click="copyToClipboard(newKeyData.key)"
									class="btn btn-square ml-2"
								>
									<ClipboardIcon class="w-4 h-4" />
								</button>
							</div>
						</div>
					</div>
					<div class="modal-action">
						<button @click="newKeyData = null" class="btn btn-primary">
							{{ t("done") }}
						</button>
					</div>
				</div>
				<div class="modal-backdrop" @click="newKeyData = null"></div>
			</div>

			<!-- 确认对话框 -->
			<div v-if="showConfirmModal" class="modal modal-open">
				<div class="modal-box">
					<h3 class="font-bold text-lg">{{ confirmModalTitle }}</h3>
					<div class="py-4">
						<p>{{ confirmModalMessage }}</p>
					</div>
					<div class="modal-action">
						<button @click="handleCancel" class="btn">
							{{ t("cancel") }}
						</button>
						<button @click="handleConfirm" class="btn btn-error">
							{{ t("confirm") }}
						</button>
					</div>
				</div>
				<div class="modal-backdrop" @click="handleCancel"></div>
			</div>
		</Teleport>
	</div>
</template>
