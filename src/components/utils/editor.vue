<template>
	<div class="relative group">
		<EditorCode
			v-if="mode === 'code'"
			v-model="localModelValue"
			:vars="vars"
			style="position: relative"
		/>
		<button 
			@click="openFullscreenEditor" 
			class="absolute top-2 right-2 btn btn-xs btn-ghost opacity-0 group-hover:opacity-100 transition-opacity duration-200"
			title="放大编辑器"
		>
			<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5v-4m0 4h-4m4 0l-5-5" />
			</svg>
		</button>
	</div>
</template>

<script setup lang="ts">
	import EditorCode from "@/components/utils/editor_code.vue";
	import { ref, computed, onMounted, onBeforeUnmount } from "vue";

	const props = defineProps({
		modelValue: {
			type: String,
			required: true,
		},
		vars: {
			type: Object,
			default: () => ({}),
		},
	});

	const emit = defineEmits(["update:modelValue"]);

	const mode = ref("code");
	const showFullscreenEditor = ref(false);

	const localModelValue = computed({
		get: () => props.modelValue,
		set: (value) => emit("update:modelValue", value),
	});

	// 这个函数被替换到下面去了

	function closeFullscreenEditor() {
		showFullscreenEditor.value = false;
		if (modalEl) {
			document.body.removeChild(modalEl);
			modalEl = null;
		}
	}

	// 创建一个放在body下的对话框
	let modalEl: HTMLElement | null = null;

	async function openFullscreenEditor() {
		showFullscreenEditor.value = true;
		
		// 创建对话框元素
		modalEl = document.createElement('div');
		modalEl.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4';
		modalEl.innerHTML = `
			<div class="bg-white rounded-lg w-full max-w-4xl h-3/4 flex flex-col">
				<div class="flex justify-between items-center p-4 border-b">
					<h3 class="text-lg font-medium">编辑器</h3>
					<button id="close-editor-modal" class="btn btn-sm btn-ghost">
						<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
						</svg>
					</button>
				</div>
				<div id="modal-editor-container" class="flex-grow p-4 overflow-hidden"></div>
			</div>
		`;

		// 添加到body
		document.body.appendChild(modalEl);

		// 添加关闭按钮事件
		const closeBtn = modalEl.querySelector('#close-editor-modal');
		if (closeBtn) {
			closeBtn.addEventListener('click', closeFullscreenEditor);
		}

		// 创建新的编辑器实例
		const editorContainer = modalEl.querySelector('#modal-editor-container');
		if (editorContainer) {
			const newEditor = document.createElement('div');
			newEditor.style.height = '100%';
			editorContainer.appendChild(newEditor);

			// 创建编辑器实例
			const EditorCode = (await import('@/components/utils/editor_code.vue')).default;
			const { createApp, h } = await import('vue');
			const app = createApp({
				setup() {
					return () => h(EditorCode, {
						modelValue: props.modelValue,
						'onUpdate:modelValue': (value: string) => {
							emit('update:modelValue', value);
						},
						vars: props.vars,
						class: 'h-full'
					});
				}
			});
			app.mount(newEditor);
		}
	}

	// 组件卸载时清理
	onBeforeUnmount(() => {
		if (modalEl && document.body.contains(modalEl)) {
			document.body.removeChild(modalEl);
		}
	});
</script>
