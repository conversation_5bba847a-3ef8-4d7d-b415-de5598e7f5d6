<template>
	<div ref="editorContainer" class="editor-container"></div>
</template>

<script setup lang="ts">
	import { ref, onMounted, watch, onBeforeUnmount } from "vue";
	import * as monaco from "monaco-editor";
	import editorWorker from "monaco-editor/esm/vs/editor/editor.worker?worker";
	import jsonWorker from "monaco-editor/esm/vs/language/json/json.worker?worker";
	import cssWorker from "monaco-editor/esm/vs/language/css/css.worker?worker";
	import htmlWorker from "monaco-editor/esm/vs/language/html/html.worker?worker";
	import tsWorker from "monaco-editor/esm/vs/language/typescript/ts.worker?worker";

	const props = defineProps({
		modelValue: String,
		vars: Object,
		language: {
			type: String,
			default: "javascript",
			required: false,
		},
	});

	const emit = defineEmits(["update:modelValue"]);

	const editorContainer = ref(null);
	let editor: monaco.editor.IStandaloneCodeEditor;
	let completionProvider: monaco.IDisposable | null = null;

	self.MonacoEnvironment = {
		getWorker(_, label) {
			if (label === "json") {
				return new jsonWorker();
			}
			if (label === "css" || label === "scss" || label === "less") {
				return new cssWorker();
			}
			if (label === "html" || label === "handlebars" || label === "razor") {
				return new htmlWorker();
			}
			if (label === "typescript" || label === "javascript") {
				return new tsWorker();
			}
			return new editorWorker();
		},
	};

	onMounted(() => {
		// 创建编辑器实例
		editor = monaco.editor.create(editorContainer.value!, {
			value: props.modelValue,
			language: props.language,
			theme: "vs-light",
			automaticLayout: true,
			fontSize: 14,
			lineNumbers: "on",
			lineNumbersMinChars: 1,
			minimap: { enabled: false },
			scrollbar: {
				vertical: "visible",
				horizontal: "visible",
			},
		});

		// 更新自动完成提供程序的函数
		const updateCompletionProvider = () => {
			if (completionProvider) {
				completionProvider.dispose();
			}
			completionProvider = monaco.languages.registerCompletionItemProvider(
				"javascript",
				{
					// @ts-ignore
					provideCompletionItems: (model, position) => {
						const suggestions = [];
						if (props.vars) {
							for (const [key, value] of Object.entries(props.vars)) {
								suggestions.push({
									label: key,
									kind: monaco.languages.CompletionItemKind.Variable,
									insertText: key,
									detail: `${typeof value}`,
									documentation: `Value: ${JSON.stringify(value)}`,
								});
							}
						}
						return { suggestions: suggestions };
					},
				}
			);
		};

		// 初始化自动完成提供程序
		updateCompletionProvider();

		editor.onDidChangeModelContent(() => {
			emit("update:modelValue", editor.getValue());
		});

		watch(
			() => props.vars,
			() => {
				// 当 vars 变化时，更新自动完成提供程序
				updateCompletionProvider();
			},
			{ deep: true }
		);
	});

	watch(
		() => props.modelValue,
		(newValue) => {
			if (editor && newValue !== editor.getValue()) {
				editor.setValue(newValue!);
			}
		}
	);

	// 清理资源
	onBeforeUnmount(() => {
		if (editor) {
			editor.dispose();
		}
		if (completionProvider) {
			completionProvider.dispose();
		}
	});
</script>

<style scoped>
	.editor-container {
		width: 100%;
		height: 200px;
		min-width: 300px;
		position: relative;
		border: 1px solid #e7e5e5;
		margin-top: 10px;
		margin-bottom: 10px;
	}
	.monaco-editor .suggest-widget {
		z-index: 100 !important;
		right: auto;
		width: 300px !important;
		left: 40px !important;
	}
</style>
