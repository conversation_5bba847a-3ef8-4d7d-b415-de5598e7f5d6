<template>
	<header
		class="bg-[#fbfcfd] py-2 px-6 flex items-center justify-between border-b border-slate-200 shadow-md"
	>
		<router-link :to="{ name: 'dashboard' }">
			<h1 class="text-xl font-bold text-neutral flex">
				<img class="w-8 h-8 block self-center shadow-sm rounded-lg" src="/icon.png" />
				<span
					class="self-center ml-2 bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent"
					>FlowAI</span
				>
			</h1>
		</router-link>
		<div class="flex items-center space-x-4">
			<button class="btn btn-sm hover:btn-primary" @click="triggerSearch">
				<MagnifyingGlassIcon class="w-4 h-4" />
			</button>
			<button
				class="btn btn-sm hover:btn-primary"
				@click="$router.push({ name: 'workflow', params: { id: 'new' } })"
			>
				<DocumentPlusIcon class="w-4 h-4" /> {{ t("create-workflow") }}
			</button>
		</div>
	</header>
</template>

<script setup lang="ts">
	import { DocumentPlusIcon, MagnifyingGlassIcon } from "@heroicons/vue/24/outline";
	import { useRouter } from "vue-router";
	import { useI18n } from "vue-i18n";
	const $router = useRouter();
	const { t } = useI18n();
	const triggerSearch = () => {
		const event = new KeyboardEvent("keydown", {
			key: "k",
			metaKey: true,
			bubbles: true,
		});
		document.dispatchEvent(event);
	};
</script>

<style scoped></style>
