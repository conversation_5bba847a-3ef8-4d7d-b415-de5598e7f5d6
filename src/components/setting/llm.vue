<script setup lang="ts">
	import { ref, onMounted, computed, reactive } from "vue";
	import { Error, Success } from "@/utils/notify";
	import {
		getManualLLMs,
		createManualLLM,
		deleteManualLLM,
		updateManualLLM,
		type ManualLLM,
	} from "@/api/manual_llm";
	import { useI18n } from "vue-i18n";
	import { PlusIcon, PencilIcon, TrashIcon, XMarkIcon } from "@heroicons/vue/24/outline";

	const { t } = useI18n();

	// 状态管理
	const manualLLMs = ref<ManualLLM[]>([]);
	const loadingState = reactive({
		isLoading: false,
		isAdding: false,
		isSaving: false,
		isDeleting: false,
	});

	// Dialog状态
	const showAddDialog = ref(false);
	const showEditDialog = ref(false);
	const showDeleteDialog = ref(false);
	const deleteTarget = ref<ManualLLM | null>(null);

	// 模型配置
	const modelProtocols = [
		{ value: "OpenAI", label: "OpenAI" },
		{ value: "Anthropic", label: "Anthropic" },
	];

	// 默认LLM配置
	const defaultLLM = {
		model_name: "",
		model_type: "OpenAI",
		model_endpoint: "https://api.openai.com/v1",
		model_key: "",
		content_limit: 1024 * 8,
		support_function_call: false,
		support_json_output: false,
		support_vision: false,
	};

	// 新建和编辑状态
	const newLLM = ref<Omit<ManualLLM, "uuid">>(structuredClone(defaultLLM));
	const editLLM = ref<ManualLLM | null>(null);

	// 高级选项展开状态
	const showAdvancedOptions = ref(false);
	const showEditAdvancedOptions = ref(false);

	// 计算属性：内容限制（KB）
	const contentLimitInK = computed({
		get: () => newLLM.value.content_limit / 1024,
		set: (val: number) => (newLLM.value.content_limit = val * 1024),
	});

	const editContentLimitInK = computed({
		get: () => {
			if (!editLLM.value) return 0;
			return editLLM.value.content_limit / 1024;
		},
		set: (val: number) => {
			if (editLLM.value) {
				editLLM.value.content_limit = val * 1024;
			}
		},
	});

	// 生命周期钩子
	onMounted(async () => {
		await loadManualLLMs();
	});

	// 数据加载
	const loadManualLLMs = async () => {
		loadingState.isLoading = true;
		try {
			const response = await getManualLLMs();
			manualLLMs.value = response.manual_llms;
		} catch (err) {
			Error(t("error"), t("get_custom_llm_error"));
		} finally {
			loadingState.isLoading = false;
		}
	};

	// Dialog相关函数
	const openAddDialog = () => {
		resetNewLLM();
		showAddDialog.value = true;
	};

	const closeAddDialog = () => {
		showAddDialog.value = false;
		resetNewLLM();
	};

	const openEditDialog = (llm: ManualLLM) => {
		editLLM.value = { ...llm };
		showEditAdvancedOptions.value = false;
		showEditDialog.value = true;
	};

	const closeEditDialog = () => {
		showEditDialog.value = false;
		editLLM.value = null;
		showEditAdvancedOptions.value = false;
	};

	const openDeleteDialog = (llm: ManualLLM) => {
		deleteTarget.value = llm;
		showDeleteDialog.value = true;
	};

	const closeDeleteDialog = () => {
		showDeleteDialog.value = false;
		deleteTarget.value = null;
	};

	// 添加LLM
	const addManualLLM = async () => {
		if (loadingState.isAdding) return;
		loadingState.isAdding = true;
		try {
			await createManualLLM(newLLM.value);
			Success(t("success"), t("add_custom_llm_success"));
			await loadManualLLMs();
			closeAddDialog();
		} catch (err) {
			Error(t("error"), t("add_custom_llm_error"));
		} finally {
			loadingState.isAdding = false;
		}
	};

	// 删除LLM
	const confirmDeleteLLM = async () => {
		if (!deleteTarget.value) return;

		loadingState.isDeleting = true;
		try {
			await deleteManualLLM(deleteTarget.value.uuid);
			Success(t("success"), t("delete_custom_llm_success"));
			manualLLMs.value = manualLLMs.value.filter(
				(llm) => llm.uuid !== deleteTarget.value!.uuid
			);
			closeDeleteDialog();
		} catch (err) {
			Error(t("error"), t("delete_custom_llm_error"));
		} finally {
			loadingState.isDeleting = false;
		}
	};

	// 保存编辑
	const saveEdit = async () => {
		if (!editLLM.value || loadingState.isSaving) return;
		loadingState.isSaving = true;
		try {
			await updateManualLLM(editLLM.value.uuid, editLLM.value);
			Success(t("success"), t("update_custom_llm_success"));
			await loadManualLLMs();
			closeEditDialog();
		} catch (err) {
			Error(t("error"), t("update_custom_llm_error"));
		} finally {
			loadingState.isSaving = false;
		}
	};

	// 重置新LLM表单
	const resetNewLLM = () => {
		newLLM.value = structuredClone(defaultLLM);
		showAdvancedOptions.value = false;
	};
</script>
<template>
	<div class="llm-config-container p-6">
		<!-- 页面标题和添加按钮 -->
		<div class="flex justify-between items-center mb-8">
			<div>
				<h1 class="text-3xl font-bold text-base-content">
					{{ t("custom_llm_management") }}
				</h1>
				<p class="text-base-content/70 mt-2">
					{{ t("manage_your_custom_llm_models") }}
				</p>
			</div>
			<button @click="openAddDialog" class="btn btn-primary gap-2">
				<PlusIcon class="w-5 h-5" />
				{{ t("add_custom_llm") }}
			</button>
		</div>

		<!-- 加载状态 -->
		<div v-if="loadingState.isLoading" class="flex justify-center my-12">
			<span class="loading loading-spinner loading-lg text-primary"></span>
		</div>

		<!-- 模型列表 -->
		<div v-else class="llm-list">
			<!-- 无模型提示 -->
			<div v-if="manualLLMs.length === 0" class="empty-state">
				<div
					class="text-center p-12 bg-gradient-to-br from-base-200 to-base-300 rounded-2xl border border-base-300"
				>
					<div
						class="w-20 h-20 mx-auto mb-6 bg-primary/10 rounded-full flex items-center justify-center"
					>
						<svg
							xmlns="http://www.w3.org/2000/svg"
							class="h-10 w-10 text-primary"
							fill="none"
							viewBox="0 0 24 24"
							stroke="currentColor"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
							/>
						</svg>
					</div>
					<h3 class="text-xl font-semibold mb-3 text-base-content">
						{{ t("no_custom_llms") }}
					</h3>
					<p class="text-base-content/60 mb-6 max-w-md mx-auto">
						{{ t("no_custom_llms_desc") }}
					</p>
					<button @click="openAddDialog" class="btn btn-primary gap-2">
						<PlusIcon class="w-5 h-5" />
						{{ t("add_custom_llm") }}
					</button>
				</div>
			</div>

			<!-- 模型卡片列表 -->
			<div v-else class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
				<div
					v-for="llm in manualLLMs"
					:key="llm.uuid"
					class="card bg-gradient-to-br from-base-100 to-base-200 shadow-lg hover:shadow-xl transition-all duration-300 border border-base-300 hover:border-primary/30"
				>
					<!-- 卡片内容 -->
					<div class="card-body p-6">
						<!-- 模型标题与类型 -->
						<div class="flex justify-between items-start mb-4">
							<div>
								<h3 class="card-title text-xl font-bold text-base-content mb-2">
									{{ llm.model_name }}
								</h3>
								<div class="badge badge-primary badge-lg">{{ llm.model_type }}</div>
							</div>
						</div>

						<!-- 模型详情 -->
						<div class="space-y-3 text-sm">
							<div class="flex items-center text-base-content/70">
								<svg
									xmlns="http://www.w3.org/2000/svg"
									class="h-4 w-4 mr-3 text-primary"
									fill="none"
									viewBox="0 0 24 24"
									stroke="currentColor"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101"
									/>
								</svg>
								<span class="truncate">{{ llm.model_endpoint }}</span>
							</div>
							<div class="flex items-center text-base-content/70">
								<svg
									xmlns="http://www.w3.org/2000/svg"
									class="h-4 w-4 mr-3 text-primary"
									fill="none"
									viewBox="0 0 24 24"
									stroke="currentColor"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
									/>
								</svg>
								<span>{{ llm.content_limit / 1024 }}K {{ t("tokens") }}</span>
							</div>
						</div>

						<!-- 功能标签 -->
						<div class="flex flex-wrap gap-2 mt-4">
							<div
								v-if="llm.support_function_call"
								class="badge badge-success badge-outline"
							>
								{{ t("function_call") }}
							</div>
							<div
								v-if="llm.support_json_output"
								class="badge badge-info badge-outline"
							>
								{{ t("json_output") }}
							</div>
							<div
								v-if="llm.support_vision"
								class="badge badge-warning badge-outline"
							>
								{{ t("vision") }}
							</div>
						</div>

						<!-- 操作按钮 -->
						<div class="card-actions justify-end mt-6 gap-2">
							<button
								@click="openEditDialog(llm)"
								class="btn btn-sm btn-primary btn-outline gap-2"
							>
								<PencilIcon class="w-4 h-4" />
								{{ t("edit") }}
							</button>
							<button
								@click="openDeleteDialog(llm)"
								class="btn btn-sm btn-error btn-outline gap-2"
							>
								<TrashIcon class="w-4 h-4" />
								{{ t("delete") }}
							</button>
						</div>
					</div>
				</div>
			</div>
		</div>

			<!-- 编辑抽屉 -->
			<div
				v-if="editLLM"
				class="edit-drawer fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-end"
			>
				<div class="bg-base-100 w-full max-w-md p-6 overflow-y-auto h-full">
					<div class="flex justify-between items-center mb-6">
						<h3 class="text-xl font-bold">{{ t("edit_custom_llm") }}</h3>
						<button @click="cancelEdit" class="btn btn-sm btn-circle">
							<svg
								xmlns="http://www.w3.org/2000/svg"
								class="h-6 w-6"
								fill="none"
								viewBox="0 0 24 24"
								stroke="currentColor"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M6 18L18 6M6 6l12 12"
								/>
							</svg>
						</button>
					</div>

					<form @submit.prevent="saveEdit" class="space-y-4">
						<!-- 基本信息 -->
						<div class="card bg-base-200 p-4 mb-4">
							<h4 class="font-medium mb-3">{{ t("basic_info") }}</h4>

							<div class="form-control">
								<label class="label">
									<span class="label-text">{{ t("model_name") }}</span>
								</label>
								<input
									v-model="editLLM.model_name"
									type="text"
									class="input input-bordered w-full"
									required
								/>
							</div>

							<div class="form-control">
								<label class="label">
									<span class="label-text">{{ t("model_protocol") }}</span>
								</label>
								<select
									v-model="editLLM.model_type"
									class="select select-bordered w-full"
									required
								>
									<option
										v-for="protocol in modelProtocols"
										:key="protocol.value"
										:value="protocol.value"
									>
										{{ protocol.label }}
									</option>
								</select>
							</div>

							<div class="form-control">
								<label class="label">
									<span class="label-text">{{ t("model_endpoint") }}</span>
								</label>
								<input
									v-model="editLLM.model_endpoint"
									type="text"
									class="input input-bordered w-full"
									required
								/>
							</div>

							<div class="form-control">
								<label class="label">
									<span class="label-text">{{ t("model_key") }}</span>
								</label>
								<input
									v-model="editLLM.model_key"
									type="password"
									class="input input-bordered w-full"
									required
								/>
							</div>
						</div>

						<!-- 高级选项 -->
						<div class="collapse collapse-arrow bg-base-200">
							<input type="checkbox" />
							<div class="collapse-title font-medium">
								{{ t("advanced_options") }}
							</div>
							<div class="collapse-content">
								<div class="form-control">
									<label class="label">
										<span class="label-text">{{
											t("context_limit")
										}}</span>
									</label>
									<input
										v-model.number="editContentLimitInK"
										type="number"
										class="input input-bordered w-full"
										min="1"
										step="1"
										required
									/>
									<label class="label">
										<span class="label-text-alt flex items-center">
											<svg
												xmlns="http://www.w3.org/2000/svg"
												fill="none"
												viewBox="0 0 24 24"
												stroke-width="1.5"
												stroke="currentColor"
												class="w-4 h-4 mr-1"
											>
												<path
													stroke-linecap="round"
													stroke-linejoin="round"
													d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z"
												/>
											</svg>
											{{ t("context_limit_description") }}
										</span>
									</label>
								</div>

								<div class="form-control">
									<label class="label cursor-pointer">
										<span class="label-text">{{
											t("support_function_call")
										}}</span>
										<input
											v-model="editLLM.support_function_call"
											type="checkbox"
											class="checkbox"
										/>
									</label>
								</div>

								<div class="form-control">
									<label class="label cursor-pointer">
										<span class="label-text">{{
											t("support_json_output")
										}}</span>
										<input
											v-model="editLLM.support_json_output"
											type="checkbox"
											class="checkbox"
										/>
									</label>
								</div>

								<div class="form-control">
									<label class="label cursor-pointer">
										<span class="label-text">{{
											t("support_vision")
										}}</span>
										<input
											v-model="editLLM.support_vision"
											type="checkbox"
											class="checkbox"
										/>
									</label>
								</div>
							</div>
						</div>

						<!-- 保存按钮 -->
						<div class="flex justify-end space-x-2 mt-6">
							<button
								type="button"
								@click="cancelEdit"
								class="btn btn-outline"
								:disabled="loadingState.isSaving"
							>
								{{ t("cancel") }}
							</button>
							<button
								type="submit"
								class="btn btn-primary"
								:disabled="loadingState.isSaving"
							>
								<span v-if="!loadingState.isSaving">{{ t("save") }}</span>
								<span v-else class="loading loading-spinner loading-sm"></span>
							</button>
						</div>
					</form>
				</div>
			</div>
		</div>

		<!-- 添加新模型选项卡 -->
		<div v-else-if="activeTab === 'add'" class="add-llm">
			<div class="card bg-base-100 shadow-lg">
				<div class="card-body">
					<h2 class="card-title mb-4">{{ t("add_custom_llm") }}</h2>

					<form @submit.prevent="addManualLLM" class="space-y-4">
						<!-- 基本信息 -->
						<div class="card bg-base-200 p-4 mb-4">
							<h4 class="font-medium mb-3">{{ t("basic_info") }}</h4>

							<div class="form-control">
								<label class="label">
									<span class="label-text">{{ t("model_name") }}</span>
								</label>
								<input
									v-model="newLLM.model_name"
									type="text"
									class="input input-bordered w-full"
									required
								/>
							</div>

							<div class="form-control">
								<label class="label">
									<span class="label-text">{{ t("model_protocol") }}</span>
								</label>
								<select
									v-model="newLLM.model_type"
									class="select select-bordered w-full"
									required
								>
									<option
										v-for="protocol in modelProtocols"
										:key="protocol.value"
										:value="protocol.value"
									>
										{{ protocol.label }}
									</option>
								</select>
								<label class="label">
									<span class="label-text-alt">{{
										t("support_model_type_desc")
									}}</span>
								</label>
							</div>

							<div class="form-control">
								<label class="label">
									<span class="label-text">{{ t("model_endpoint") }}</span>
								</label>
								<input
									v-model="newLLM.model_endpoint"
									type="text"
									class="input input-bordered w-full"
									:placeholder="t('endpoint_placeholder')"
									required
								/>
							</div>

							<div class="form-control">
								<label class="label">
									<span class="label-text">{{ t("model_key") }}</span>
								</label>
								<input
									v-model="newLLM.model_key"
									type="password"
									class="input input-bordered w-full"
									required
								/>
							</div>
						</div>

						<!-- 高级选项 -->
						<div class="collapse collapse-arrow bg-base-200">
							<input type="checkbox" v-model="showAdvancedOptions" />
							<div class="collapse-title font-medium">
								{{ t("advanced_options") }}
							</div>
							<div class="collapse-content">
								<div class="form-control">
									<label class="label">
										<span class="label-text">{{
											t("context_limit")
										}}</span>
									</label>
									<input
										v-model.number="contentLimitInK"
										type="number"
										class="input input-bordered w-full"
										min="1"
										step="1"
										required
									/>
									<label class="label">
										<span class="label-text-alt flex items-center">
											<svg
												xmlns="http://www.w3.org/2000/svg"
												fill="none"
												viewBox="0 0 24 24"
												stroke-width="1.5"
												stroke="currentColor"
												class="w-4 h-4 mr-1"
											>
												<path
													stroke-linecap="round"
													stroke-linejoin="round"
													d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z"
												/>
											</svg>
											{{ t("context_limit_description") }}
										</span>
									</label>
								</div>

								<div class="form-control">
									<label class="label cursor-pointer">
										<span class="label-text">{{
											t("support_function_call")
										}}</span>
										<input
											v-model="newLLM.support_function_call"
											type="checkbox"
											class="checkbox"
										/>
									</label>
								</div>

								<div class="form-control">
									<label class="label cursor-pointer">
										<span class="label-text">{{
											t("support_json_output")
										}}</span>
										<input
											v-model="newLLM.support_json_output"
											type="checkbox"
											class="checkbox"
										/>
									</label>
								</div>

								<div class="form-control">
									<label class="label cursor-pointer">
										<span class="label-text">{{
											t("support_vision")
										}}</span>
										<input
											v-model="newLLM.support_vision"
											type="checkbox"
											class="checkbox"
										/>
									</label>
								</div>
							</div>
						</div>

						<!-- 提交按钮 -->
						<div class="card-actions justify-end mt-6">
							<button
								type="submit"
								class="btn btn-primary"
								:disabled="loadingState.isAdding"
							>
								<span v-if="!loadingState.isAdding">{{
									t("add_custom_llm")
								}}</span>
								<span v-else class="loading loading-spinner loading-sm"></span>
							</button>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>
</template>
