<script setup lang="ts">
	import { ref, onMounted, computed, reactive } from "vue";
	import { Error, Success } from "@/utils/notify";
	import {
		getManualLLMs,
		createManualLLM,
		deleteManualLLM,
		updateManualLLM,
		type ManualLLM,
	} from "@/api/manual_llm";
	import { useI18n } from "vue-i18n";

	const { t } = useI18n();

	// 状态管理
	const activeTab = ref("existing"); // 'existing' 或 'add'
	const manualLLMs = ref<ManualLLM[]>([]);
	const loadingState = reactive({
		isLoading: false,
		isAdding: false,
		isSaving: false,
		isDeleting: false,
	});

	// 模型配置
	const modelProtocols = [
		{ value: "OpenAI", label: "OpenAI" },
		{ value: "Anthropic", label: "Anthropic" },
	];

	// 默认LLM配置
	const defaultLLM = {
		model_name: "",
		model_type: "OpenAI",
		model_endpoint: "https://api.openai.com/v1",
		model_key: "",
		content_limit: 1024 * 8,
		support_function_call: false,
		support_json_output: false,
		support_vision: false,
	};

	// 新建和编辑状态
	const newLLM = ref<Omit<ManualLLM, "uuid">>(structuredClone(defaultLLM));
	const editLLM = ref<ManualLLM | null>(null);
	const editingId = ref<string | null>(null);

	// 高级选项展开状态
	const showAdvancedOptions = ref(false);

	// 计算属性：内容限制（KB）
	const contentLimitInK = computed({
		get: () => newLLM.value.content_limit / 1024,
		set: (val: number) => (newLLM.value.content_limit = val * 1024),
	});

	const editContentLimitInK = computed({
		get: () => {
			if (!editLLM.value) return 0;
			return editLLM.value.content_limit / 1024;
		},
		set: (val: number) => {
			if (editLLM.value) {
				editLLM.value.content_limit = val * 1024;
			}
		},
	});

	// 生命周期钩子
	onMounted(async () => {
		await loadManualLLMs();
	});

	// 数据加载
	const loadManualLLMs = async () => {
		loadingState.isLoading = true;
		try {
			const response = await getManualLLMs();
			manualLLMs.value = response.manual_llms;
		} catch (err) {
			Error(t("error"), t("get_custom_llm_error"));
		} finally {
			loadingState.isLoading = false;
		}
	};

	// 添加LLM
	const addManualLLM = async () => {
		if (loadingState.isAdding) return;
		loadingState.isAdding = true;
		try {
			await createManualLLM(newLLM.value);
			Success(t("success"), t("add_custom_llm_success"));
			await loadManualLLMs();
			resetNewLLM();
			activeTab.value = "existing"; // 添加成功后切换到已有模型选项卡
		} catch (err) {
			Error(t("error"), t("add_custom_llm_error"));
		} finally {
			loadingState.isAdding = false;
		}
	};

	// 删除LLM
	const deleteCustomLLM = async (uuid: string) => {
		if (!confirm(t("confirm_delete_custom_llm"))) {
			return;
		}

		loadingState.isDeleting = true;
		try {
			await deleteManualLLM(uuid);
			Success(t("success"), t("delete_custom_llm_success"));
			manualLLMs.value = manualLLMs.value.filter((llm) => llm.uuid !== uuid);
		} catch (err) {
			Error(t("error"), t("delete_custom_llm_error"));
		} finally {
			loadingState.isDeleting = false;
		}
	};

	// 重置新LLM表单
	const resetNewLLM = () => {
		newLLM.value = structuredClone(defaultLLM);
		showAdvancedOptions.value = false;
	};

	// 开始编辑LLM
	const startEdit = (llm: ManualLLM) => {
		editLLM.value = { ...llm };
		editingId.value = llm.uuid;
	};

	// 取消编辑
	const cancelEdit = () => {
		editLLM.value = null;
		editingId.value = null;
	};

	// 保存编辑
	const saveEdit = async () => {
		if (!editLLM.value || loadingState.isSaving) return;
		loadingState.isSaving = true;
		try {
			await updateManualLLM(editLLM.value.uuid, editLLM.value);
			Success(t("success"), t("update_custom_llm_success"));
			await loadManualLLMs();
			cancelEdit();
		} catch (err) {
			Error(t("error"), t("update_custom_llm_error"));
		} finally {
			loadingState.isSaving = false;
		}
	};

	// 切换选项卡
	const switchTab = (tab: string) => {
		activeTab.value = tab;
		// 切换到添加选项卡时重置表单
		if (tab === "add") {
			resetNewLLM();
		}
	};
</script>
<template>
	<div class="llm-config-container">
		<!-- 选项卡导航 -->
		<div class="tabs tabs-boxed mb-6">
			<a
				class="tab"
				:class="{ 'tab-active': activeTab === 'existing' }"
				@click="switchTab('existing')"
			>
				{{ t("existing_custom_llm") }}
				<span v-if="manualLLMs.length > 0" class="badge badge-sm ml-2">{{
					manualLLMs.length
				}}</span>
			</a>
			<a
				class="tab"
				:class="{ 'tab-active': activeTab === 'add' }"
				@click="switchTab('add')"
			>
				{{ t("add_custom_llm") }}
			</a>
		</div>

		<!-- 加载状态 -->
		<div v-if="loadingState.isLoading" class="flex justify-center my-8">
			<span class="loading loading-spinner loading-lg"></span>
		</div>

		<!-- 已有模型选项卡 -->
		<div v-else-if="activeTab === 'existing'" class="existing-llms">
			<!-- 无模型提示 -->
			<div v-if="manualLLMs.length === 0" class="empty-state">
				<div class="text-center p-8 bg-base-200 rounded-lg">
					<svg
						xmlns="http://www.w3.org/2000/svg"
						class="h-12 w-12 mx-auto mb-4 text-gray-400"
						fill="none"
						viewBox="0 0 24 24"
						stroke="currentColor"
					>
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
						/>
					</svg>
					<h3 class="text-lg font-medium mb-2">{{ t("no_custom_llms") }}</h3>
					<p class="text-gray-500 mb-4">{{ t("no_custom_llms_desc") }}</p>
					<button @click="switchTab('add')" class="btn btn-primary">
						{{ t("add_custom_llm") }}
					</button>
				</div>
			</div>

			<!-- 模型卡片列表 -->
			<div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
				<div
					v-for="llm in manualLLMs"
					:key="llm.uuid"
					class="card bg-base-100 shadow-md hover:shadow-lg transition-shadow"
				>
					<!-- 卡片内容 -->
					<div class="card-body p-4">
						<!-- 模型标题与类型 -->
						<div class="flex justify-between items-start mb-2">
							<h3 class="card-title text-lg">
								{{ llm.model_name }}
								<div class="badge badge-accent">{{ llm.model_type }}</div>
							</h3>

							<!-- 编辑中状态 -->
							<div v-if="editingId === llm.uuid" class="badge badge-primary">
								{{ t("editing") }}
							</div>
						</div>

						<!-- 模型详情 -->
						<div class="space-y-1 text-sm">
							<p class="flex items-center">
								<svg
									xmlns="http://www.w3.org/2000/svg"
									class="h-4 w-4 mr-2 text-gray-500"
									fill="none"
									viewBox="0 0 24 24"
									stroke="currentColor"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101"
									/>
								</svg>
								{{ llm.model_endpoint }}
							</p>
							<p class="flex items-center">
								<svg
									xmlns="http://www.w3.org/2000/svg"
									class="h-4 w-4 mr-2 text-gray-500"
									fill="none"
									viewBox="0 0 24 24"
									stroke="currentColor"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
									/>
								</svg>
								{{ llm.content_limit / 1024 }}K {{ t("tokens") }}
							</p>
						</div>

						<!-- 功能标签 -->
						<div class="flex flex-wrap gap-1 mt-2">
							<div
								v-if="llm.support_function_call"
								class="badge badge-outline badge-sm"
							>
								{{ t("function_call") }}
							</div>
							<div
								v-if="llm.support_json_output"
								class="badge badge-outline badge-sm"
							>
								{{ t("json_output") }}
							</div>
							<div
								v-if="llm.support_vision"
								class="badge badge-outline badge-sm"
							>
								{{ t("vision") }}
							</div>
						</div>

						<!-- 操作按钮 -->
						<div class="card-actions justify-end mt-4">
							<button
								@click="startEdit(llm)"
								class="btn btn-sm btn-outline"
								:disabled="editingId !== null"
							>
								{{ t("edit") }}
							</button>
							<button
								@click="deleteCustomLLM(llm.uuid)"
								class="btn btn-sm btn-error btn-outline"
								:disabled="loadingState.isDeleting || editingId === llm.uuid"
							>
								<span v-if="!loadingState.isDeleting">{{ t("delete") }}</span>
								<span v-else class="loading loading-spinner loading-xs"></span>
							</button>
						</div>
					</div>
				</div>
			</div>

			<!-- 编辑抽屉 -->
			<div
				v-if="editLLM"
				class="edit-drawer fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-end"
			>
				<div class="bg-base-100 w-full max-w-md p-6 overflow-y-auto h-full">
					<div class="flex justify-between items-center mb-6">
						<h3 class="text-xl font-bold">{{ t("edit_custom_llm") }}</h3>
						<button @click="cancelEdit" class="btn btn-sm btn-circle">
							<svg
								xmlns="http://www.w3.org/2000/svg"
								class="h-6 w-6"
								fill="none"
								viewBox="0 0 24 24"
								stroke="currentColor"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M6 18L18 6M6 6l12 12"
								/>
							</svg>
						</button>
					</div>

					<form @submit.prevent="saveEdit" class="space-y-4">
						<!-- 基本信息 -->
						<div class="card bg-base-200 p-4 mb-4">
							<h4 class="font-medium mb-3">{{ t("basic_info") }}</h4>

							<div class="form-control">
								<label class="label">
									<span class="label-text">{{ t("model_name") }}</span>
								</label>
								<input
									v-model="editLLM.model_name"
									type="text"
									class="input input-bordered w-full"
									required
								/>
							</div>

							<div class="form-control">
								<label class="label">
									<span class="label-text">{{ t("model_protocol") }}</span>
								</label>
								<select
									v-model="editLLM.model_type"
									class="select select-bordered w-full"
									required
								>
									<option
										v-for="protocol in modelProtocols"
										:key="protocol.value"
										:value="protocol.value"
									>
										{{ protocol.label }}
									</option>
								</select>
							</div>

							<div class="form-control">
								<label class="label">
									<span class="label-text">{{ t("model_endpoint") }}</span>
								</label>
								<input
									v-model="editLLM.model_endpoint"
									type="text"
									class="input input-bordered w-full"
									required
								/>
							</div>

							<div class="form-control">
								<label class="label">
									<span class="label-text">{{ t("model_key") }}</span>
								</label>
								<input
									v-model="editLLM.model_key"
									type="password"
									class="input input-bordered w-full"
									required
								/>
							</div>
						</div>

						<!-- 高级选项 -->
						<div class="collapse collapse-arrow bg-base-200">
							<input type="checkbox" />
							<div class="collapse-title font-medium">
								{{ t("advanced_options") }}
							</div>
							<div class="collapse-content">
								<div class="form-control">
									<label class="label">
										<span class="label-text">{{
											t("context_limit")
										}}</span>
									</label>
									<input
										v-model.number="editContentLimitInK"
										type="number"
										class="input input-bordered w-full"
										min="1"
										step="1"
										required
									/>
									<label class="label">
										<span class="label-text-alt flex items-center">
											<svg
												xmlns="http://www.w3.org/2000/svg"
												fill="none"
												viewBox="0 0 24 24"
												stroke-width="1.5"
												stroke="currentColor"
												class="w-4 h-4 mr-1"
											>
												<path
													stroke-linecap="round"
													stroke-linejoin="round"
													d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z"
												/>
											</svg>
											{{ t("context_limit_description") }}
										</span>
									</label>
								</div>

								<div class="form-control">
									<label class="label cursor-pointer">
										<span class="label-text">{{
											t("support_function_call")
										}}</span>
										<input
											v-model="editLLM.support_function_call"
											type="checkbox"
											class="checkbox"
										/>
									</label>
								</div>

								<div class="form-control">
									<label class="label cursor-pointer">
										<span class="label-text">{{
											t("support_json_output")
										}}</span>
										<input
											v-model="editLLM.support_json_output"
											type="checkbox"
											class="checkbox"
										/>
									</label>
								</div>

								<div class="form-control">
									<label class="label cursor-pointer">
										<span class="label-text">{{
											t("support_vision")
										}}</span>
										<input
											v-model="editLLM.support_vision"
											type="checkbox"
											class="checkbox"
										/>
									</label>
								</div>
							</div>
						</div>

						<!-- 保存按钮 -->
						<div class="flex justify-end space-x-2 mt-6">
							<button
								type="button"
								@click="cancelEdit"
								class="btn btn-outline"
								:disabled="loadingState.isSaving"
							>
								{{ t("cancel") }}
							</button>
							<button
								type="submit"
								class="btn btn-primary"
								:disabled="loadingState.isSaving"
							>
								<span v-if="!loadingState.isSaving">{{ t("save") }}</span>
								<span v-else class="loading loading-spinner loading-sm"></span>
							</button>
						</div>
					</form>
				</div>
			</div>
		</div>

		<!-- 添加新模型选项卡 -->
		<div v-else-if="activeTab === 'add'" class="add-llm">
			<div class="card bg-base-100 shadow-lg">
				<div class="card-body">
					<h2 class="card-title mb-4">{{ t("add_custom_llm") }}</h2>

					<form @submit.prevent="addManualLLM" class="space-y-4">
						<!-- 基本信息 -->
						<div class="card bg-base-200 p-4 mb-4">
							<h4 class="font-medium mb-3">{{ t("basic_info") }}</h4>

							<div class="form-control">
								<label class="label">
									<span class="label-text">{{ t("model_name") }}</span>
								</label>
								<input
									v-model="newLLM.model_name"
									type="text"
									class="input input-bordered w-full"
									required
								/>
							</div>

							<div class="form-control">
								<label class="label">
									<span class="label-text">{{ t("model_protocol") }}</span>
								</label>
								<select
									v-model="newLLM.model_type"
									class="select select-bordered w-full"
									required
								>
									<option
										v-for="protocol in modelProtocols"
										:key="protocol.value"
										:value="protocol.value"
									>
										{{ protocol.label }}
									</option>
								</select>
								<label class="label">
									<span class="label-text-alt">{{
										t("support_model_type_desc")
									}}</span>
								</label>
							</div>

							<div class="form-control">
								<label class="label">
									<span class="label-text">{{ t("model_endpoint") }}</span>
								</label>
								<input
									v-model="newLLM.model_endpoint"
									type="text"
									class="input input-bordered w-full"
									:placeholder="t('endpoint_placeholder')"
									required
								/>
							</div>

							<div class="form-control">
								<label class="label">
									<span class="label-text">{{ t("model_key") }}</span>
								</label>
								<input
									v-model="newLLM.model_key"
									type="password"
									class="input input-bordered w-full"
									required
								/>
							</div>
						</div>

						<!-- 高级选项 -->
						<div class="collapse collapse-arrow bg-base-200">
							<input type="checkbox" v-model="showAdvancedOptions" />
							<div class="collapse-title font-medium">
								{{ t("advanced_options") }}
							</div>
							<div class="collapse-content">
								<div class="form-control">
									<label class="label">
										<span class="label-text">{{
											t("context_limit")
										}}</span>
									</label>
									<input
										v-model.number="contentLimitInK"
										type="number"
										class="input input-bordered w-full"
										min="1"
										step="1"
										required
									/>
									<label class="label">
										<span class="label-text-alt flex items-center">
											<svg
												xmlns="http://www.w3.org/2000/svg"
												fill="none"
												viewBox="0 0 24 24"
												stroke-width="1.5"
												stroke="currentColor"
												class="w-4 h-4 mr-1"
											>
												<path
													stroke-linecap="round"
													stroke-linejoin="round"
													d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z"
												/>
											</svg>
											{{ t("context_limit_description") }}
										</span>
									</label>
								</div>

								<div class="form-control">
									<label class="label cursor-pointer">
										<span class="label-text">{{
											t("support_function_call")
										}}</span>
										<input
											v-model="newLLM.support_function_call"
											type="checkbox"
											class="checkbox"
										/>
									</label>
								</div>

								<div class="form-control">
									<label class="label cursor-pointer">
										<span class="label-text">{{
											t("support_json_output")
										}}</span>
										<input
											v-model="newLLM.support_json_output"
											type="checkbox"
											class="checkbox"
										/>
									</label>
								</div>

								<div class="form-control">
									<label class="label cursor-pointer">
										<span class="label-text">{{
											t("support_vision")
										}}</span>
										<input
											v-model="newLLM.support_vision"
											type="checkbox"
											class="checkbox"
										/>
									</label>
								</div>
							</div>
						</div>

						<!-- 提交按钮 -->
						<div class="card-actions justify-end mt-6">
							<button
								type="submit"
								class="btn btn-primary"
								:disabled="loadingState.isAdding"
							>
								<span v-if="!loadingState.isAdding">{{
									t("add_custom_llm")
								}}</span>
								<span v-else class="loading loading-spinner loading-sm"></span>
							</button>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>
</template>
