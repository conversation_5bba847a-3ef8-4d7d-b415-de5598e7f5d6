<template>
	<div v-if="nodeData">
		<p class="mt-4">
			{{ t("node_name") }}: <NodeName :nodeId="prop.id" v-model="nodeData.data.name" />
		</p>

		<p class="mt-4">{{ t("model") }}:</p>
		<select class="select select-bordered w-full" v-model="nodeData.data.input.model">
			<option v-for="i in llmModels" :value="i.value">{{ i.name }}</option>
		</select>
		<div v-if="supportVision && vars.filter((v) => v.type === 'image').length > 0">
			<p class="mt-4">{{ t("context_image") }}:</p>
			<select
				class="select select-bordered w-full"
				v-model="nodeData.data.input.image_content"
				:disabled="!supportVision"
			>
				<option value="">{{ t("not_use_image") }}</option>
				<option
					v-for="i in vars.filter((v) => v.type === 'image')"
					:value="'$' + i.node + '.' + i.name"
				>
					${{ i.node + "." + i.name }}
				</option>
			</select>
			<!-- <p class="mt-1 text-xs text-gray-400">* 只有支持视觉功能的模型才能处理图片输入</p> -->
		</div>
		<p class="mt-4">{{ t("prompt") }}:</p>
		<component_wait>
			<editor v-model="nodeData.data.input.prompt" :vars="vars"></editor>
		</component_wait>
		<p class="mt-4">{{ t("system_prompt") }}:</p>
		<component_wait>
			<editor v-model="nodeData.data.input.system" :vars="vars"></editor>
		</component_wait>
		<div class="divider m-0"></div>
		<div class="collapse collapse-arrow p-0 rounded-none relative">
			<input type="checkbox" />
			<div class="collapse-title pl-2">{{ t("more_config") }}</div>
			<div class="collapse-content relative">
				<p class="mt-0">
					{{ t("json_output") }}:<input
						:disabled="
							!llmModels.find(
								(i) => nodeData && i.value == nodeData.data.input.model
							)?.support_json_output
						"
						type="checkbox"
						class="toggle toggle-primary align-middle toggle-sm"
						:checked="nodeData.data.input.json_response"
						@change="changeJson"
					/>
				</p>
				<p class="mt-1 text-xs text-gray-400">
					* {{ t("enable_json_output_warning") }}
				</p>

				<p class="mt-4">
					{{ t("max_output_length") }}:
					<input
						type="number"
						class="input input-bordered input-sm w-32"
						v-model="nodeData.data.input.max_tokens"
						min="0"
					/>
				</p>
				<p class="mt-1 text-xs text-gray-400">
					* {{ t("max_output_length_warning") }}
				</p>
			</div>
		</div>
		<div class="divider"></div>
		<p>{{ t("output") }}:</p>
		<div
			v-for="i in nodeData.data.output"
			:key="i"
			class="mt-2 px-5 py-3 rounded-lg bg-slate-50 font-thin border-l-2 border-slate-200"
		>
			<span class="font-bold block">${{ nodeData.data.name }}.{{ i.name }}</span>
			<p class="m-0 text-xs font-thin">{{ i.desc }}</p>
		</div>
	</div>
</template>

<script setup lang="ts">
	import { useNodesData } from "@vue-flow/core";
	import { ref, watch, onMounted, defineAsyncComponent, computed } from "vue";
	import getLinkedVar from "@/utils/linkedNodeVars";
	import NodeName from "@/components/utils/node_name_input.vue";
	import component_wait from "@/components/utils/component_wait.vue";
	import { getLLMModels } from "@/utils/models";
	import { useI18n } from "vue-i18n";
	const { t } = useI18n();
	const editor = defineAsyncComponent(() => import("@/components/utils/editor.vue"));
	let prop = defineProps(["id"]);
	let nodeData = useNodesData(prop.id);
	let vars = ref(getLinkedVar(prop.id));
	let llmModels = ref<any[]>([]);

	onMounted(async () => {
		// 获取模型列表
		llmModels.value = await getLLMModels();

		// 确保nodeData存在
		if (!nodeData.value) return;

		// 初始化模型
		if (!nodeData.value.data.input.model && llmModels.value.length > 0) {
			nodeData.value.data.input.model = llmModels.value[0].value;
		}

		// 初始化最大token数
		if (!nodeData.value.data.input.max_tokens) {
			nodeData.value.data.input.max_tokens = 0;
		}

		// 初始化图片相关配置
		if (!nodeData.value.data.input.image_content) {
			nodeData.value.data.input.image_content = "";
		}

		// 如果不支持视觉或没有图片变量,清空图片内容
		if (!supportVision.value || !vars.value.some((v) => v.type === "image")) {
			nodeData.value.data.input.image_content = "";
		}

		// 如果模型不支持JSON输出,关闭JSON响应
		const currentModel = llmModels.value.find(
			(i) => i.value === nodeData.value?.data.input.model
		);
		if (!currentModel?.support_json_output) {
			nodeData.value.data.input.json_response = false;
		}
	});

	let changeJson = (e: any) => {
		if (nodeData.value != null) {
			if (
				!llmModels.value.find((i) => i.value == nodeData.value?.data.input.model)
					?.support_json_output
			) {
				nodeData.value.data.input.json_response = false;
			}
			nodeData.value.data.input.json_response =
				!nodeData.value?.data.input.json_response;
		}
	};

	let supportVision = computed(() => {
		return llmModels.value.find((i) => i.value == nodeData.value?.data.input.model)
			?.support_vision;
	});

	// 添加模型切换监听
	watch(
		() => nodeData.value?.data.input.model,
		(newVal) => {
			const currentModel = llmModels.value.find((i) => i.value === newVal);

			// 清理vision相关值
			if (!currentModel?.support_vision && nodeData.value?.data.input.image_content) {
				nodeData.value.data.input.image_content = "";
			}

			// 清理json设置
			if (
				!currentModel?.support_json_output &&
				nodeData.value?.data.input.json_response
			) {
				nodeData.value.data.input.json_response = false;
			}
		}
	);
</script>
<style scoped></style>
