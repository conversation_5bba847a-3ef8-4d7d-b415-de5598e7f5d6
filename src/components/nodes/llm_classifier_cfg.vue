<template>
	<div v-if="nodeData">
		<p class="mt-2">{{ t("set_llm_classifier_class") }}</p>
		<p class="mt-4">
			{{ t("node_name") }}: <NodeName :nodeId="prop.id" v-model="nodeData.data.name" />
		</p>
		<p class="mt-4">{{ t("model") }}:</p>
		<select class="select select-bordered w-full" v-model="nodeData.data.input.model">
			<option
				v-for="i in llmModels.filter((i) => i.support_function_call)"
				:key="i.value"
				:value="i.value"
			>
				{{ i.name }}
			</option>
		</select>
		<div v-if="supportVision && vars.filter((v) => v.type === 'image').length > 0">
			<p class="mt-4">{{ t("context_image") }}:</p>
			<select
				class="select select-bordered w-full"
				v-model="nodeData.data.input.image_content"
				:disabled="!supportVision"
			>
				<option value="">{{ t("not_use_image") }}</option>
				<option
					v-for="i in vars.filter((v) => v.type === 'image')"
					:value="'$' + i.node + '.' + i.name"
				>
					${{ i.node + "." + i.name }}
				</option>
			</select>
		</div>
		<p class="mt-4">{{ t("select_judgment_content") }}:</p>
		<select
			class="select select-bordered w-full"
			v-model="nodeData.data.input.condition_content"
		>
			<option disabled selected value="">--{{ t("pls-select") }}--</option>
			<option v-for="i in vars" :value="'$' + i.node + '.' + i.name">
				${{ i.node + "." + i.name }}
			</option>
		</select>
		<p class="mt-4">{{ t("classifier_class") }}:</p>
		<p
			class="mb-2 bg-slate-50 text-sm rounded-lg py-3 text-center text-gray-500"
			v-if="!nodeData.data.input.class || nodeData.data.input.class.length == 0"
		>
			{{ t("please_add_class") }}
		</p>
		<div
			v-for="(i, index) in nodeData.data.input.class"
			:key="i"
			class="flex justify-center items-center mt-3 first-of-type:mt-0 bg-gray-50 rounded-full py-3"
		>
			<span class="mr-1 font-light">{{ t("class") }} {{ index + 1 }}</span>
			<input
				type="text"
				:placeholder="t('class_name')"
				v-model="i.name"
				class="input input-sm input-bordered w-3/5 max-w-xs box-border ml-1"
			/>
			<button
				class="btn btn-warning btn-xs rounded-full ml-2 align-middle"
				@click="inDelProp(index)"
			>
				-
			</button>
		</div>
		<button class="btn btn-primary mt-3 w-full" @click="inAddProp">
			+ {{ t("add") }}
		</button>

		<p class="mt-4">{{ t("classifier_prompt") }}:</p>
		<p class="mt-0 mb-3 text-xs font-thin">
			{{ t("can_be_empty") }}
		</p>
		<component_wait>
			<editor v-model="nodeData.data.input.prompt" :vars="vars"></editor>
		</component_wait>
		<p class="mt-0 mb-3 text-xs font-thin" v-html="t('can_use_variables')"></p>
	</div>
</template>

<script setup lang="ts">
	import { useNodesData } from "@vue-flow/core";
	import getLinkedVar from "@/utils/linkedNodeVars";
	import { ref, defineAsyncComponent, onMounted, computed } from "vue";
	import { getLLMModels } from "@/utils/models";
	import NodeName from "@/components/utils/node_name_input.vue";
	import component_wait from "@/components/utils/component_wait.vue";
	import { useI18n } from "vue-i18n";
	const { t } = useI18n();
	const editor = defineAsyncComponent(() => import("@/components/utils/editor.vue"));
	const prop = defineProps(["id"]);
	const nodeData = useNodesData(prop.id);
	const vars = ref(getLinkedVar(prop.id) || []);
	const llmModels = ref<any[]>([]);

	onMounted(async () => {
		llmModels.value = await getLLMModels();

		// 确保nodeData存在
		if (!nodeData.value) return;

		// 初始化模型
		if (!nodeData.value.data.input.model && llmModels.value.length > 0) {
			nodeData.value.data.input.model = llmModels.value[0].value;
		}

		// 初始化图片输入
		if (!nodeData.value.data.input.image_content) {
			nodeData.value.data.input.image_content = "";
		}

		// 如果不支持视觉或没有图片变量,清空图片内容
		if (!supportVision.value || !vars.value.some((v) => v.type === "image")) {
			nodeData.value.data.input.image_content = "";
		}
	});

	let supportVision = computed(() => {
		return llmModels.value.find((i) => i.value == nodeData.value?.data.input.model)
			?.support_vision;
	});

	let inAddProp = () => {
		let allClass = nodeData.value?.data.input.class || [];
		let id = "llm_classifier_class_" + Math.random().toString(36).slice(-8);
		allClass.push({ name: t("class") + (allClass.length + 1), id: id });
		if (nodeData.value) {
			nodeData.value.data.input.class = allClass;
		}
	};
	let inDelProp = (i: any) => {
		let allClass = nodeData.value?.data.input.class || [];
		allClass.splice(allClass.indexOf(i), 1);
		if (nodeData.value) {
			nodeData.value.data.output.class = allClass;
		}
	};
</script>
<style scoped></style>
