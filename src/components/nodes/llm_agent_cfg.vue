<template>
	<div v-if="nodeData">
		<p class="mt-4">
			{{ t("node_name") }}: <NodeName :nodeId="prop.id" v-model="nodeData.data.name" />
		</p>

		<p class="mt-4">{{ t("model") }}:</p>
		<select class="select select-bordered w-full" v-model="nodeData.data.input.model">
			<option
				v-for="i in llmModels.filter((i) => i.support_function_call)"
				:value="i.value"
			>
				{{ i.name }}
			</option>
		</select>

		<p class="mt-4">{{ t("prompt") }}:</p>
		<component_wait>
			<editor v-model="nodeData.data.input.prompt" :vars="vars"></editor>
		</component_wait>

		<p class="mt-4">{{ t("output_lang") }}:</p>
		<select
			class="select select-bordered w-full"
			v-model="nodeData.data.input.output_lang"
		>
			<option value="zh">中文</option>
			<option value="en">English</option>
			<option value="ja">日本語</option>
			<option value="ko">한국어</option>
			<option value="fr">Français</option>
			<option value="de">Deutsch</option>
			<option value="es">Español</option>
			<option value="ru">Русский</option>
		</select>

		<p class="mt-4">{{ t("built_in_tools") }}:</p>
		<div class="mt-2">
			<multi-select v-model="nodeData.data.input.tools" :options="availableTools" />
		</div>

		<p class="mt-4">{{ t("max_iterations") }}:</p>
		<input
			type="number"
			class="input input-bordered w-full"
			v-model="nodeData.data.input.max_iterations"
			min="1"
			max="50"
		/>
		<p class="mt-1 text-xs text-gray-400">* {{ t("max_iterations_desc") }}</p>

		<div class="collapse mt-4 border rounded-box">
			<input type="checkbox" />
			<div class="collapse-title font-medium flex gap-4 items-center">
				<span>{{ t("mcp_servers") }}</span>
			</div>
			<div class="collapse-content">
				<!-- 没有服务器时的提示 -->
				<div
					v-if="
						!nodeData.data.input.mcp_servers ||
						nodeData.data.input.mcp_servers.length === 0
					"
					class="text-center py-3 text-gray-500"
				>
					<div class="text-sm">{{ t("no_mcp_servers") }}</div>
					<button @click="addMCPServer" class="btn btn-sm btn-outline mt-2">
						{{ t("add_mcp_server") }}
					</button>
				</div>

				<!-- 服务器列表 -->
				<div
					v-for="(server, index) in nodeData.data.input.mcp_servers"
					:key="index"
					class="mt-2 p-2 border rounded-md"
				>
					<div class="flex justify-between items-center">
						<span class="text-xs text-gray-400">#{{ index + 1 }}</span>
						<span class="font-medium">{{
							server.name || t("unnamed_server")
						}}</span>
						<button
							@click="removeMCPServer(index)"
							class="btn btn-sm btn-circle btn-ghost"
						>
							×
						</button>
					</div>

					<div class="mt-2">
						<p class="text-xs">{{ t("server_name") }}:</p>
						<input
							type="text"
							class="input input-bordered input-sm w-full"
							v-model="server.name"
						/>
					</div>

					<div class="mt-2">
						<p class="text-xs">{{ t("server_url") }}:</p>
						<input
							type="text"
							class="input input-bordered input-sm w-full"
							v-model="server.url"
						/>
					</div>

					<div class="mt-2">
						<p class="text-xs">{{ t("server_type") }}:</p>
						<select
							class="select select-bordered select-sm w-full"
							v-model="server.type"
						>
							<option value="sse">SSE</option>
							<option value="streamable-http">Streamable HTTP</option>
						</select>
					</div>

					<!-- 自定义 HTTP 头 -->
					<div class="mt-2">
						<div class="flex justify-between items-center">
							<p class="text-xs">{{ t("server_headers") }}:</p>
							<div
								class="badge badge-neutral badge-sm"
								v-if="Object.keys(server.headers || {}).length > 0"
							>
								{{ Object.keys(server.headers || {}).length }}
							</div>
						</div>
						<div class="border rounded-md p-2 mt-1">
							<div
								v-for="(value, key) in server.headers || {}"
								:key="key.toString()"
								class="flex gap-2 mt-1"
							>
								<input
									type="text"
									class="input input-bordered input-sm w-1/2"
									v-model="server._tempHeaderKeys[key]"
									@blur="updateHeaderKey(server, key.toString())"
									placeholder="键名"
								/>
								<input
									type="text"
									class="input input-bordered input-sm w-1/2"
									v-model="server.headers[key]"
									placeholder="值"
								/>
								<button
									@click="removeHeader(server, key.toString())"
									class="btn btn-sm btn-circle btn-ghost"
								>
									×
								</button>
							</div>
							<button
								@click="addHeader(server)"
								class="btn btn-sm btn-outline mt-2 w-full"
							>
								{{ t("add_header") }}
							</button>
						</div>
					</div>
				</div>

				<!-- 添加服务器按钮 -->
				<div
					v-if="
						nodeData.data.input.mcp_servers &&
						nodeData.data.input.mcp_servers.length > 0
					"
					class="mt-2"
				>
					<button @click="addMCPServer" class="btn btn-sm btn-outline w-full">
						{{ t("add_mcp_server") }}
					</button>
				</div>
			</div>
		</div>

		<div class="divider"></div>
		<p>{{ t("output") }}:</p>
		<div
			v-for="i in nodeData.data.output"
			:key="i"
			class="mt-2 px-5 py-3 rounded-lg bg-slate-50 font-thin border-l-2 border-slate-200"
		>
			<span class="font-bold block">${{ nodeData.data.name }}.{{ i.name }}</span>
			<p class="m-0 text-xs font-thin">{{ i.desc }}</p>
		</div>
	</div>
</template>

<script setup lang="ts">
	import { useNodesData } from "@vue-flow/core";
	import { ref, onMounted, defineAsyncComponent } from "vue";
	import getLinkedVar from "@/utils/linkedNodeVars";
	import NodeName from "@/components/utils/node_name_input.vue";
	import component_wait from "@/components/utils/component_wait.vue";
	import MultiSelect from "@/components/utils/multi_select.vue";
	import { getLLMModels } from "@/utils/models";
	import { useI18n } from "vue-i18n";
	const { t, locale } = useI18n();
	const editor = defineAsyncComponent(() => import("@/components/utils/editor.vue"));
	let prop = defineProps(["id"]);
	let nodeData = useNodesData(prop.id);
	let vars = ref(getLinkedVar(prop.id));
	let llmModels = ref<any[]>([]);

	const availableTools = ref([
		{ value: "fetch_web", name: t("fetch_web") },
		{ value: "http_call", name: t("http_call") },
		{ value: "calculator", name: t("calculator") },
	]);

	// MCP服务器相关方法
	function addMCPServer() {
		if (!nodeData.value) return;

		if (!nodeData.value.data.input.mcp_servers) {
			nodeData.value.data.input.mcp_servers = [];
		}
		nodeData.value.data.input.mcp_servers.push({
			name: "",
			url: "",
			type: "sse",
			headers: {},
			_tempHeaderKeys: {},
		});
	}

	function removeMCPServer(index: number) {
		if (!nodeData.value) return;

		nodeData.value.data.input.mcp_servers.splice(index, 1);
	}

	// 处理 headers 相关函数
	function addHeader(server: any) {
		if (!server.headers) {
			server.headers = {};
		}
		if (!server._tempHeaderKeys) {
			server._tempHeaderKeys = {};
		}

		const newKey = `header_${Object.keys(server.headers).length}`;
		server.headers[newKey] = "";
		server._tempHeaderKeys[newKey] = newKey;
	}

	function removeHeader(server: any, key: string) {
		if (!server.headers) return;

		delete server.headers[key];
		if (server._tempHeaderKeys) {
			delete server._tempHeaderKeys[key];
		}
	}

	function updateHeaderKey(server: any, oldKey: string) {
		if (!server.headers || !server._tempHeaderKeys) return;

		const newKey = server._tempHeaderKeys[oldKey];
		if (newKey && newKey !== oldKey) {
			const value = server.headers[oldKey];
			delete server.headers[oldKey];
			server.headers[newKey] = value;

			// 更新临时键名映射
			delete server._tempHeaderKeys[oldKey];
			server._tempHeaderKeys[newKey] = newKey;
		}
	}

	onMounted(async () => {
		// 获取模型列表
		llmModels.value = await getLLMModels();

		// 确保nodeData存在
		if (!nodeData.value) return;

		// 初始化模型
		if (!nodeData.value.data.input.model && llmModels.value.length > 0) {
			nodeData.value.data.input.model = llmModels.value[0].value;
		}

		// 初始化工具列表
		if (
			!nodeData.value.data.input.tools ||
			!Array.isArray(nodeData.value.data.input.tools)
		) {
			nodeData.value.data.input.tools = [];
		}

		// 初始化输出语言，使用用户当前语言设置
		if (!nodeData.value.data.input.output_lang) {
			nodeData.value.data.input.output_lang = locale.value;
		}

		// 初始化最大迭代次数
		if (!nodeData.value.data.input.max_iterations) {
			nodeData.value.data.input.max_iterations = 5;
		}

		// 初始化MCP服务器列表
		if (!nodeData.value.data.input.mcp_servers) {
			nodeData.value.data.input.mcp_servers = [];
		} else {
			// 确保每个服务器都有 headers 和 _tempHeaderKeys 属性
			nodeData.value.data.input.mcp_servers.forEach((server: any) => {
				if (!server.headers) {
					server.headers = {};
				}
				if (!server._tempHeaderKeys) {
					server._tempHeaderKeys = {};
					// 初始化 _tempHeaderKeys，使其与当前的 headers 键一致
					Object.keys(server.headers).forEach((key) => {
						server._tempHeaderKeys[key] = key;
					});
				}
			});
		}
	});
</script>
<style scoped></style>
