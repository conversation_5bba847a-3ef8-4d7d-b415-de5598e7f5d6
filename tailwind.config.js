/** @type {import('tailwindcss').Config} */
export default {
	purge: ["./index.html", "./src/**/*.{vue,js,ts,jsx,tsx}"],
	content: [],
	theme: {
		extend: {},
	},
	plugins: [require("daisyui")],
	daisyui: {
		themes: [
			{
				light: {
					primary: "#3b82f6",
					"primary-content": "#ffffff",
					secondary: "#8b5cf6",
					"secondary-content": "#ffffff",
					accent: "#06b6d4",
					"accent-content": "#ffffff",
					neutral: "#374151",
					"neutral-content": "#ffffff",
					"base-100": "#ffffff",
					"base-200": "#f8fafc",
					"base-300": "#e2e8f0",
					"base-content": "#1f2937",
					info: "#0ea5e9",
					"info-content": "#ffffff",
					success: "#10b981",
					"success-content": "#ffffff",
					warning: "#f59e0b",
					"warning-content": "#ffffff",
					error: "#ef4444",
					"error-content": "#ffffff",
				},
			},
		],
	},
};
